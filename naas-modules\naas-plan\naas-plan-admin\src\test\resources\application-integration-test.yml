# 集成测试配置
# 需要真实的S3服务（MinIO或AWS S3）
#定时任务,powerjob5配置，但在37用的是powerjob4
# powerjob:
#   worker:
#     akka-port: 27781
#     app-name: naas-plan
#     password: naas-plan
#     server-address: ***********:7600/powerjob5
#     store-strategy: disk
#     max-result-length: 4096
#     max-appended-wf-context-length: 4096
log:
  suffix: .txt
powerjob:
  worker:
    akka-port: 27781
    app-name: naas-plan
    server-address: ***********:7700/powerjob
    store-strategy: disk
    max-result-length: 4096
    max-appended-wf-context-length: 4096
cxf:
  path: /services
  servlet:
    load-on-startup: -1

spring:
  flyway:
    # 是否启用flyway
    enabled: true
    # flyway 的 clean 命令会删除指定 schema 下的所有 table, 生产务必禁掉。这个默认值是 false 理论上作为默认配置是不科学的。
    clean-disabled: true
    #占位符替换
    placeholder-replacement: false
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: classpath:db/migration
    #  metadata 版本控制信息表 默认 flyway_schema_history
    table: flyway_schema_history_plan
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 执行迁移时是否自动调用验证   当你的 版本不符合逻辑 比如 你先执行了 DML 而没有 对应的DDL 会抛出异常
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 如果没有 flyway_schema_history 这个 metadata 表， 在执行 flyway migrate 命令之前, 必须先执行 flyway baseline 命令
    # 设置为 true 后 flyway 将在需要 baseline 的时候, 自动执行一次 baseline。
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    # 指定 baseline 的版本号,默认值为 1, 低于该版本号的 SQL 文件, migrate 时会被忽略
    baseline-version: 1
    # 是否允许不按顺序迁移 开发建议 true  生产建议 false
    out-of-order: false
    # 需要 flyway 管控的 schema list,这里我们配置为flyway  缺省的话, 使用spring.datasource.url 配置的那个 schema,
    # 可以指定多个schema, 但仅会在第一个schema下建立 metadata 表, 也仅在第一个schema应用migration sql 脚本.
    # 但flyway Clean 命令会依次在这些schema下都执行一遍. 所以 确保生产 spring.flyway.clean-disabled 为 true
    # schemas: flyway
  test:
    database:
          replace: none
  autoconfigure:
    exclude:
          - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
          - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
s3:
  # 真实S3服务配置（需要根据实际环境修改）
  endpoint: ${INTEGRATION_S3_ENDPOINT:http://***********:9010/}
  accessKey: ${INTEGRATION_S3_ACCESS_KEY:cmcc_minio}
  secretKey: ${INTEGRATION_S3_SECRET_KEY:cmcc_minio}
  region: ${INTEGRATION_S3_REGION:us-east-1}
  https: ${INTEGRATION_S3_HTTPS:false}
  maxConnections: 50
  bucketName: ${INTEGRATION_S3_BUCKET:naas-file}

  # 连接超时配置（集成测试使用较短超时）
  connectionTimeout: 5000
  socketTimeout: 10000
  requestTimeout: 30000

  # 连接池配置
  maxIdleTime: 30000
  validateAfterInactivity: 2000

  # HTTP配置
  useGzip: true
  useExpectContinue: false

  # 集成测试重试配置
  retry:
    # 默认重试配置
    default:
      maxRetries: 2
      baseDelay: 500
      maxDelay: 5000
      strategy: EXPONENTIAL_BACKOFF
      enableJitter: true
      jitterFactor: 0.1

    # 按操作类型的重试配置
    operationSpecific:
      upload:
        maxRetries: 3
        baseDelay: 1000
        maxDelay: 10000
        strategy: EXPONENTIAL_BACKOFF
        enableJitter: true
        jitterFactor: 0.15

      download:
        maxRetries: 2
        baseDelay: 500
        maxDelay: 5000
        strategy: LINEAR_BACKOFF
        enableJitter: true
        jitterFactor: 0.1

      metadata:
        maxRetries: 1
        baseDelay: 200
        maxDelay: 2000
        strategy: FIXED_DELAY
        fixedDelay: 1000
        enableJitter: false

    # 按错误类型的重试配置
    errorSpecific:
      connection_timeout:
        maxRetries: 3
        baseDelay: 1000
        strategy: EXPONENTIAL_BACKOFF

      read_timeout:
        maxRetries: 2
        baseDelay: 800
        strategy: LINEAR_BACKOFF

      socket_timeout:
        maxRetries: 2
        baseDelay: 600
        strategy: EXPONENTIAL_BACKOFF

      connection_refused:
        maxRetries: 4
        baseDelay: 2000
        strategy: EXPONENTIAL_BACKOFF

      network_unreachable:
        maxRetries: 5
        baseDelay: 3000
        strategy: FIXED_DELAY
        fixedDelay: 3000

    # 自适应配置
    adaptive:
      successRateThreshold: 0.7
      adjustmentFactor: 1.3
      windowSize: 50
      minSampleSize: 5

    # 熔断器配置（集成测试中禁用）
    enableCircuitBreaker: false

# 日志配置
logging:
  level:
    cn.cmcc.common.storage: DEBUG
    cn.cmcc.common.storage.service.RetryMetricsService: INFO
    cn.cmcc.common.storage.service.EnhancedRetryExecutor: DEBUG
    com.amazonaws: WARN
    org.apache.http: WARN
    root: INFO

  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 测试配置
test:
  # 集成测试超时配置
  timeout:
    upload: 30000      # 上传超时30秒
    download: 20000    # 下载超时20秒
    concurrent: 60000  # 并发测试超时60秒

  # 测试数据配置
  data:
    smallFileSize: 1024      # 1KB
    mediumFileSize: 102400   # 100KB
    largeFileSize: 1048576   # 1MB

  # 并发测试配置
  concurrency:
    maxThreads: 20
    defaultConcurrency: 10
    highConcurrency: 50

# Spring Boot测试配置

# 管理端点配置（用于集成测试监控）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,storage-retry-metrics
  endpoint:
    health:
      show-details: always
    storage-retry-metrics:
      enabled: true

  # 指标配置
  metrics:
    export:
      simple:
        enabled: true
    enable:
      jvm: true
      system: true
      process: true

# 集成测试环境变量说明
# 运行集成测试前需要设置以下环境变量：
#
# INTEGRATION_TEST_ENABLED=true                    # 启用集成测试
# INTEGRATION_S3_ENDPOINT=http://***********:9010    # S3服务端点
# INTEGRATION_S3_ACCESS_KEY=cmcc_minio             # S3访问密钥
# INTEGRATION_S3_SECRET_KEY=cmcc_minio             # S3秘密密钥
# INTEGRATION_S3_BUCKET=naas-file    # S3存储桶名称
# INTEGRATION_S3_REGION=us-east-1                  # S3区域
# INTEGRATION_S3_HTTPS=false                       # 是否使用HTTPS
#
# MinIO服务启动命令示例：
# docker run -p 9000:9000 -p 9001:9001 \
#   -e "MINIO_ROOT_USER=cmcc_minio" \
#   -e "MINIO_ROOT_PASSWORD=cmcc_minio" \
#   minio/minio server /data --console-address ":9001"
