package cn.cmcc.plan.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 单验URL校验配置类
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "single.validate.url.validation")
public class SingleValidateUrlValidationConfig {

    /**
     * 是否启用URL校验，默认启用
     */
    private boolean enabled = true;

    /**
     * 连接超时时间（毫秒），默认10秒
     */
    private int connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒），默认30秒
     */
    private int readTimeout = 30000;

    /**
     * 异步校验超时时间（秒），默认30秒
     */
    private int asyncTimeoutSeconds = 30;

    /**
     * 是否启用并行校验，默认启用
     */
    private boolean parallelValidation = false;

    /**
     * 最大重试次数，默认1次
     */
    private int maxRetries = 1;

    /**
     * 是否记录详细日志，默认false
     */
    private boolean verboseLogging = false;
}
