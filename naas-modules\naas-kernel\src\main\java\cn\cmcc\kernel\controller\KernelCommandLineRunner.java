package cn.cmcc.kernel.controller;

import cn.cmcc.kernel.service.ITCommonQueryService;
import cn.cmcc.system.api.service.RemoteFieldConfigService;
import cn.cmcc.system.api.service.RemoteSysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class KernelCommandLineRunner implements CommandLineRunner {

    @Autowired
    private ITCommonQueryService tCommonQueryService;

    @DubboReference
    private RemoteFieldConfigService remoteFieldConfigService;

    @DubboReference
    private RemoteSysConfigService remoteSysConfigService;

    @Override
    public void run(String... args) throws Exception {
        try {
            tCommonQueryService.refreshQueryCache("ALL");
            remoteFieldConfigService.refreshCache();
            remoteSysConfigService.reloadConfig();
            log.info("程序初始化：kernel刷新缓存");
        } catch (Exception e) {
            log.error("程序初始化：kernel刷新缓存失败: {}", e.getMessage());
        }
    }
}
